'use client';

import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import Flag from 'react-country-flag';

export default function LanguageSwitcher() {
    const { i18n } = useTranslation();
    const router = useRouter();

    const changeLanguage = async (lng: string) => {
        console.log("&&&&&&&&&&&&&&&**********lng");
        console.log(lng);
        // i18n.changeLanguage(lng);
        document.documentElement.lang = lng;
        document.documentElement.dir = lng === 'he' || lng === 'ar' ? 'rtl' : 'ltr';

        await fetch('/api/set-lang', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ lang: lng }),
        });

        router.refresh();
    };

    const languages = [
        { code: 'en', flag: <Flag countryCode="US" svg style={{ width: '20px', height: '20px' }} />, label: 'English' },
        { code: 'he', flag: <Flag countryCode="IL" svg style={{ width: '20px', height: '20px' }} />, label: 'עברית' },
        { code: 'ar', flag: <Flag countryCode="SA" svg style={{ width: '20px', height: '20px' }} />, label: 'العربية' },
        { code: 'zh', flag: <Flag countryCode="CN" svg style={{ width: '20px', height: '20px' }} />, label: '中文' },
    ];

    return (
        <div className="flex gap-2">
            {languages.map((lang) => (
                <button
                    key={lang.code}
                    onClick={() => changeLanguage(lang.code)}
                    className={`flex items-center justify-center w-8 h-8 rounded-full p-0 border-none bg-transparent transition-all duration-200 ${
                        i18n.language === lang.code
                            ? 'border-4 border-[var(--primary-button)] !bg-[var(--primary-button)]/20 ring-2 ring-[var(--primary-button)]/50'
                            : 'hover:border-2 hover:border-[var(--primary)] hover:bg-gray-100/50'
                    } hover:scale-105 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] rtl:transform-none`}
                    aria-label={`Switch to ${lang.label}`}
                    title={lang.label}
                >
                    {lang.flag}
                </button>
            ))}
        </div>
    );
}