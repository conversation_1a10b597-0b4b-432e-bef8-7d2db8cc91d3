"use client";

import {Lawsuit} from "@/types/lawsuit";
import {motion} from "framer-motion";
import {useState, useEffect} from "react";
import moment from "moment";
import "moment/locale/he";
import "moment/locale/ar";
import Link from "next/link";
import {useRouter} from "next/navigation";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {Button} from "@/components/ui/button";
import {Switch} from "@/components/ui/switch";
import {Textarea} from "@/components/ui/textarea";
import {Input} from "@/components/ui/input";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {zodResolver} from "@hookform/resolvers/zod";
import {useForm} from "react-hook-form";
import {z} from "zod";
import {useTranslation} from "react-i18next";
import {toast} from "sonner";
import {
    ScaleIcon,
    ArrowDownTrayIcon,
    ArrowRightIcon,
    ArrowLeftIcon,
    UserIcon,
    IdentificationIcon,
    CakeIcon,
    MapPinIcon,
    PhoneIcon,
    DevicePhoneMobileIcon,
    PrinterIcon,
    BriefcaseIcon,
    EnvelopeIcon,
    HashtagIcon,
    CalendarIcon,
    TagIcon,
    CurrencyDollarIcon,
    ClipboardDocumentCheckIcon,
    BanknotesIcon,
    WalletIcon,
    ReceiptPercentIcon,
    UsersIcon,
    ChatBubbleLeftIcon,
    ChatBubbleLeftRightIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/solid";
import {useSession} from "next-auth/react";
import {useInView} from "react-intersection-observer";

export const cardVariants = {
    hidden: {opacity: 0, y: 10},
    visible: {opacity: 1, y: 0, transition: {duration: 0.3, ease: "easeOut"}},
    hover: {boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)", transition: {duration: 0.2}},
};

const formSchema = z.object({
    file_number: z.string().min(1, {message: "file_number_required"}),
    tovea: z.string().min(1, {message: "plaintiff_required"}),
    nitba: z.string().min(1, {message: "defendant_required"}),
    status: z.string().min(1, {message: "defendant_required"}),
    date_meeting: z.string().optional(),
    tovea_id: z.string().optional(),
    tovea_birth: z.string().optional(),
    tovea_living_city: z.string().optional(),
    claimant_attr_telephone: z.string().optional(),
    claimant_attr_cellphone: z.string().optional(),
    claimant_attr_fax: z.string().optional(),
    nitba_id: z.string().optional(),
    nitba_lawyer: z.string().optional(),
    claimed_attorney_email: z.string().optional(),
    claimed_attr_telephone: z.string().optional(),
    claimed_attr_cellphone: z.string().optional(),
    claimed_attr_fax: z.string().optional(),
    amount: z.string().optional(),
    result_case: z.string().optional(),
    final_amount: z.string().optional(),
    lwr_final_amount: z.string().optional(),
    expenses: z.string().optional(),
    is_mutual_consent: z.string().optional(),
    cancellation_reason: z.string().optional(),
});

const cancelSchema = z.object({
    isMutualConsent: z.boolean(),
    cancellationReason: z.string().min(1, {message: "cancellation_reason_required"}),
    settlementAgreement: z.instanceof(File).optional(),
});

const historySchema = z.object({
    newStatus: z.string().optional(),
    message: z.string().min(1, {message: "message_required"}),
    notifyClient: z.boolean(),
});

interface FormValues {
    file_number: string;
    tovea: string;
    nitba: string;
    status: string;
    date_meeting?: string;
    tovea_id?: string;
    tovea_birth?: string;
    tovea_living_city?: string;
    claimant_attr_telephone?: string;
    claimant_attr_cellphone?: string;
    claimant_attr_fax?: string;
    nitba_id?: string;
    nitba_lawyer?: string;
    claimed_attorney_email?: string;
    claimed_attr_telephone?: string;
    claimed_attr_cellphone?: string;
    claimed_attr_fax?: string;
    amount?: string;
    result_case?: string;
    final_amount?: string;
    lwr_final_amount?: string;
    expenses?: string;
    is_mutual_consent?: string;
    cancellation_reason?: string;
}

type CancelFormValues = z.infer<typeof cancelSchema>;
type HistoryFormValues = z.infer<typeof historySchema>;

interface LawsuitEditClientProps {
    lawsuit: Lawsuit;
}

interface HistoryEntry {
    id: string;
    old_status: string | null;
    new_status: string | null;
    message: string;
    created: string;
    created_by: string;
    notify_client: boolean;
}

export default function LawsuitEditClient({lawsuit}: LawsuitEditClientProps) {
    const {t} = useTranslation();
    moment.locale(lawsuit.lang || "he");
    const router = useRouter();
    const {data: session, status} = useSession();
    const [isAdminOrEditor, setIsAdminOrEditor] = useState(false);
    const [history, setHistory] = useState<HistoryEntry[]>([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const {ref, inView} = useInView();
    const [openCancelDialog, setOpenCancelDialog] = useState(false);
    const [openHistoryPopup, setOpenHistoryPopup] = useState(false);

    const statusOptions = [
        {value: "none", label: t("select_status", lawsuit.lang === "ar" ? "اختر الوضع" : "----------")},
        {
            value: "20",
            label: t("not_member", lawsuit.lang === "ar" ? "غير عضو في الاتحاد" : "אינו חבר בהתאחדות"),
            stringValue: "not_member",
        },
        {value: "7", label: t("duplicate", lawsuit.lang === "ar" ? "تكرار" : "כפילות"), stringValue: "duplicate"},
        {value: "19", label: t("not_found", lawsuit.lang === "ar" ? "غير موجود" : "לא אותר"), stringValue: "not_found"},
        {
            value: "17",
            label: t("pending", lawsuit.lang === "ar" ? "في انتظار الجلسة" : "ממתין לדיון"),
            stringValue: "pending",
        },
        {
            value: "2",
            label: t("awaiting_scheduling", lawsuit.lang === "ar" ? "في انتظار تحديد موعد" : "ממתין לתיאום מועד דיון"),
            stringValue: "awaiting_scheduling",
        },
        {
            value: "21",
            label: t("needs_review", lawsuit.lang === "ar" ? "يحتاج فحص" : "מצריך בדיקה"),
            stringValue: "needs_review",
        },
        {
            value: "5",
            label: t("closed_returned", lawsuit.lang === "ar" ? "مغلق - رُجع للمحكمة" : "سגור - הוחזר לבית הדין"),
            stringValue: "closed_returned",
        },
        {
            value: "18",
            label: t("closed_settlement", lawsuit.lang === "ar" ? "مغلق - تسوية" : "סגور – פשרה"),
            stringValue: "closed_settlement",
        },
        {
            value: "22",
            label: t("closed_external_settlement", lawsuit.lang === "ar" ? "مغلق - تسوية خارج اللجنة" : "סגור - פשרה מחוץ לועדה"),
            stringValue: "closed_external_settlement",
        },
        {
            value: "1",
            label: t("open_needs_change", lawsuit.lang === "ar" ? "مفتوح (يحتاج تعديل)" : "פתוח (צריך לשנות)"),
            stringValue: "open_needs_change",
        },
        {value: "canceled", label: t("canceled", lawsuit.lang === "ar" ? "ملغى" : "בוטל"), stringValue: "canceled"},
    ];

    const getStatusValue = (status: string | undefined): string => {
        if (!status) return "none";
        const option = statusOptions.find((opt) => opt.stringValue === status || opt.value === status);
        return option ? option.value : "none";
    };

    const [currentStatus, setCurrentStatus] = useState(getStatusValue(lawsuit.status));

    const form = useForm<FormValues, any, FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            file_number: lawsuit.file_number || "",
            tovea: lawsuit.tovea || "",
            nitba: lawsuit.nitba || "",
            status: getStatusValue(lawsuit.status),
            date_meeting: lawsuit.date_meeting || "",
            tovea_id: lawsuit.tovea_id || "",
            tovea_birth: lawsuit.tovea_birth || "",
            tovea_living_city: lawsuit.tovea_living_city || "",
            claimant_attr_telephone: lawsuit.claimant_attr_telephone || "",
            claimant_attr_cellphone: lawsuit.claimant_attr_cellphone || "",
            claimant_attr_fax: lawsuit.claimant_attr_fax || "",
            nitba_id: lawsuit.nitba_id || "",
            nitba_lawyer: lawsuit.nitba_lawyer || "",
            claimed_attorney_email: lawsuit.claimed_attorney_email || "",
            claimed_attr_telephone: lawsuit.claimed_attr_telephone || "",
            claimed_attr_cellphone: lawsuit.claimed_attr_cellphone || "",
            claimed_attr_fax: lawsuit.claimed_attr_fax || "",
            amount: lawsuit.amount || "",
            result_case: lawsuit.result_case || "",
            final_amount: lawsuit.final_amount || "",
            lwr_final_amount: lawsuit.lwr_final_amount || "",
            expenses: lawsuit.expenses || "",
            is_mutual_consent: lawsuit.is_mutual_consent || "",
            cancellation_reason: lawsuit.cancellation_reason || "",
        },
    });

    const cancelForm = useForm<CancelFormValues>({
        resolver: zodResolver(cancelSchema),
        defaultValues: {
            isMutualConsent: lawsuit.is_mutual_consent === "1",
            cancellationReason: lawsuit.cancellation_reason || "",
            settlementAgreement: undefined,
        },
    });

    const historyForm = useForm<HistoryFormValues>({
        resolver: zodResolver(historySchema),
        defaultValues: {
            newStatus: "",
            message: "",
            notifyClient: false,
        },
    });

    // Check user roles
    useEffect(() => {
        if (status === "authenticated" && session?.accessToken) {
            const checkRoles = async () => {
                try {
                    const response = await fetch("/api/check-roles", {
                        headers: {Authorization: `Bearer ${session.accessToken}`},
                    });
                    if (response.ok) {
                        const data = await response.json();
                        setIsAdminOrEditor(data.isAdminOrEditor || false);
                    } else {
                        console.error("Failed to check roles:", await response.json());
                        toast.error(t("error"), {description: t("failed_to_check_roles")});
                    }
                } catch (error) {
                    console.error("Error checking roles:", error);
                    toast.error(t("error"), {description: t("unexpected_error")});
                }
            };
            checkRoles();
        }
    }, [session, status, t]);

    const fetchHistory = async (pageNum: number, reset = false) => {
        if (!lawsuit.id) {
            console.error("No valid id found for lawsuit:", lawsuit);
            toast.error(t("error"), {
                description: t("missing_id"),
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
            return;
        }

        if (!session?.accessToken) return;

        setIsLoading(true);
        try {
            const response = await fetch(`/api/lawsuit/${lawsuit.id}/history?page=${pageNum}&per_page=10`, {
                headers: {Authorization: `Bearer ${session.accessToken}`},
            });
            if (response.ok) {
                const data = await response.json();
                const newHistory = data.history || [];
                setHistory((prev) => (reset ? newHistory : [...prev, ...newHistory]));
                setHasMore(newHistory.length === 10);
            } else {
                console.error("Failed to fetch history:", await response.json());
                toast.error(t("error"), {description: t("failed_to_fetch_history")});
            }
        } catch (error) {
            console.error("Error fetching history:", error);
            toast.error(t("error"), {description: t("unexpected_error")});
        } finally {
            setIsLoading(false);
        }
    };

    // Initial history fetch
    useEffect(() => {
        if (isAdminOrEditor && openHistoryPopup) {
            setHistory([]);
            setPage(1);
            fetchHistory(1, true);
        }
    }, [isAdminOrEditor, openHistoryPopup]);

    // Infinite scroll
    useEffect(() => {
        if (inView && hasMore && !isLoading) {
            const nextPage = page + 1;
            setPage(nextPage);
            fetchHistory(nextPage);
        }
    }, [inView, hasMore, isLoading, page]);

    const onSubmit = async (values: FormValues) => {
        if (!session?.accessToken) {
            toast.error(t("error"), {
                description: t("missing_auth"),
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
            return;
        }

        setIsLoading(true);
        try {
            const payload = {
                ...values,
                status: values.status === "none" ? "" : values.status,
                is_mutual_consent: values.is_mutual_consent === "1" ? true : false,
                date_meeting: values.date_meeting || null,
                tovea_id: values.tovea_id || null,
                tovea_birth: values.tovea_birth || null,
                tovea_living_city: values.tovea_living_city || null,
                claimant_attr_telephone: values.claimant_attr_telephone || null,
                claimant_attr_cellphone: values.claimant_attr_cellphone || null,
                claimant_attr_fax: values.claimant_attr_fax || null,
                nitba_id: values.nitba_id || null,
                nitba_lawyer: values.nitba_lawyer || null,
                claimed_attorney_email: values.claimed_attorney_email || null,
                claimed_attr_telephone: values.claimed_attr_telephone || null,
                claimed_attr_cellphone: values.claimed_attr_cellphone || null,
                claimed_attr_fax: values.claimed_attr_fax || null,
                amount: values.amount || null,
                result_case: values.result_case || null,
                final_amount: values.final_amount || null,
                lwr_final_amount: values.lwr_final_amount || null,
                expenses: values.expenses || null,
                cancellation_reason: values.cancellation_reason || null,
            };

            const response = await fetch(`/api/lawsuit/${lawsuit.id}`, {
                method: "PATCH",
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || t("failed_to_update_lawsuit"));
            }

            toast.success(t("success"), {
                description: t("lawsuit_updated_successfully"),
                style: {
                    background: "var(--gold-light)",
                    color: "#111827",
                    border: "1px solid var(--gold-light)",
                },
            });
            router.push("/dashboard/lawsuits");
        } catch (err: unknown) {
            const errorMessage = err instanceof Error ? err.message : t("unexpected_error");
            toast.error(t("error"), {
                description: errorMessage,
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
        } finally {
            setIsLoading(false);
        }
    };

    const onCancelSubmit = async (values: CancelFormValues) => {
        if (!lawsuit.id) {
            toast.error(t("error"), {
                description: t("missing_id"),
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
            return;
        }
        const formData = new FormData();
        formData.append("isMutualConsent", values.isMutualConsent ? "1" : "0");
        formData.append("cancellationReason", values.cancellationReason);
        if (values.settlementAgreement) {
            formData.append("settlementAgreement", values.settlementAgreement);
        }

        try {
            const response = await fetch(`/api/cancel-case?sid=${lawsuit.id}`, {
                method: "POST",
                body: formData,
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || t("cancellation_failed"));
            }

            toast.success(t("success"), {
                description: t("case_canceled_successfully"),
                style: {
                    background: "var(--gold-light)",
                    color: "#111827",
                    border: "1px solid var(--gold-light)",
                },
            });
            setOpenCancelDialog(false);
            cancelForm.reset();
            setCurrentStatus("canceled");
            router.refresh();
        } catch (err: unknown) {
            const errorMessage = err instanceof Error ? err.message : t("unexpected_error");
            toast.error(t("error"), {
                description: errorMessage,
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
        }
    };

    const onHistorySubmit = async (values: HistoryFormValues) => {
        if (!lawsuit.id) {
            toast.error(t("error"), {
                description: t("missing_id"),
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
            return;
        }
        try {
            const newStatus = values.newStatus === "none" ? "" : values.newStatus;
            const response = await fetch(`/api/lawsuit/${lawsuit.id}/history`, {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify({
                    new_status: newStatus || null,
                    message: values.message,
                    notify_client: values.notifyClient,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || t("history_add_failed"));
            }

            setHistory([]);
            setPage(1);
            fetchHistory(1, true);

            toast.success(t("success"), {
                description: t("history_added"),
                style: {
                    background: "var(--gold-light)",
                    color: "#111827",
                    border: "1px solid var(--gold-light)",
                },
            });
            historyForm.reset();
            if (newStatus) {
                setCurrentStatus(newStatus);
            }
        } catch (err: unknown) {
            const errorMessage = err instanceof Error ? err.message : t("unexpected_error");
            toast.error(t("error"), {
                description: errorMessage,
                style: {
                    background: "var(--red-elegant)",
                    color: "#ffffff",
                    border: "1px solid var(--red-elegant)",
                },
            });
        }
    };

    const isCanceled = currentStatus === "canceled";

    return (
        <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-800 text-white py-12 px-4 sm:px-6 lg:px-8">
            <div className="mx-auto max-w-5xl">
                <motion.div
                    className="mb-6"
                    initial={{opacity: 0, x: lawsuit.lang === "he" || lawsuit.lang === "ar" ? 20 : -20}}
                    animate={{opacity: 1, x: 0}}
                    transition={{duration: 0.4}}
                >
                    <Link
                        href="/dashboard/lawsuits"
                        className="inline-block text-[var(--gold-light)] font-heebo text-lg font-semibold hover:text-[var(--gold-light)]/80"
                    >
                        <span className="flex items-center gap-2">
                            {lawsuit.lang === "he" || lawsuit.lang === "ar" ? (
                                <>
                                    <ArrowRightIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                    {t("back_to_lawsuits")}
                                </>
                            ) : (
                                <>
                                    <ArrowLeftIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                    {t("back_to_lawsuits")}
                                </>
                            )}
                        </span>
                    </Link>
                </motion.div>

                <motion.h1
                    className="text-4xl md:text-5xl font-heebo font-bold text-center text-[var(--gold-light)] mb-8 tracking-wide flex items-center justify-center gap-3"
                    initial={{opacity: 0, y: -10}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.4}}
                >
                    <ScaleIcon className="w-8 h-8 text-[var(--gold-light)]"/>
                    {t("edit_lawsuit")} #{lawsuit.file_number || lawsuit.id}
                </motion.h1>

                {isAdminOrEditor && (
                    <motion.div
                        className="fixed bottom-8 right-8 z-50"
                        initial={{opacity: 0, scale: 0.8}}
                        animate={{opacity: 1, scale: 1}}
                        transition={{duration: 0.4}}
                    >
                        <Dialog open={openHistoryPopup} onOpenChange={setOpenHistoryPopup}>
                            <DialogTrigger asChild>
                                <Button
                                    className="bg-[var(--gold-light)] text-gray-900 hover:bg-[var(--gold-light)]/80 rounded-full p-4 shadow-xl"
                                    title={t("view_history")}
                                >
                                    <DocumentTextIcon className="w-8 h-8"/>
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="bg-gray-800 text-white border-[var(--gold-light)]/20 max-w-3xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                    <DialogTitle
                                        className="text-2xl font-heebo font-bold text-[var(--gold-light)] flex items-center gap-2">
                                        <ChatBubbleLeftRightIcon className="w-6 h-6"/>
                                        {t("history")}
                                    </DialogTitle>
                                </DialogHeader>
                                <div className="space-y-6 p-4">
                                    <Form {...historyForm}>
                                        <form onSubmit={historyForm.handleSubmit(onHistorySubmit)}
                                              className="space-y-4">
                                            <FormField
                                                control={historyForm.control}
                                                name="message"
                                                render={({field}) => (
                                                    <FormItem>
                                                        <FormLabel
                                                            className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                            <ChatBubbleLeftIcon
                                                                className="w-5 h-5 text-[var(--gold-light)]"/>
                                                            {t("note")}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Textarea
                                                                {...field}
                                                                className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)] min-h-[100px]"
                                                                placeholder={t("enter_note")}
                                                            />
                                                        </FormControl>
                                                        <FormMessage/>
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                control={historyForm.control}
                                                name="newStatus"
                                                render={({field}) => (
                                                    <FormItem>
                                                        <FormLabel
                                                            className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                            <TagIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                            {t("new_status")}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <select
                                                                {...field}
                                                                className="w-full bg-gray-700 text-white rounded-md p-2 border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                            >
                                                                {statusOptions.map((option) => (
                                                                    <option key={option.value} value={option.value}>
                                                                        {option.label}
                                                                    </option>
                                                                ))}
                                                            </select>
                                                        </FormControl>
                                                        <FormMessage/>
                                                    </FormItem>
                                                )}
                                            />
                                            <FormField
                                                control={historyForm.control}
                                                name="notifyClient"
                                                render={({field}) => (
                                                    <FormItem className="flex items-center space-x-4">
                                                        <FormLabel
                                                            className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                            <EnvelopeIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                            {t("notify_client")}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Switch
                                                                checked={field.value}
                                                                onCheckedChange={field.onChange}
                                                                className="data-[state=checked]:bg-[var(--gold-light)] data-[state=unchecked]:bg-[var(--red-elegant)]"
                                                            />
                                                        </FormControl>
                                                        <FormMessage/>
                                                    </FormItem>
                                                )}
                                            />
                                            <Button
                                                type="submit"
                                                className="bg-[var(--gold-light)] text-gray-900 hover:bg-[var(--gold-light)]/80 w-full"
                                            >
                                                {t("add_note")}
                                            </Button>
                                        </form>
                                    </Form>

                                    <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-2">
                                        {history.length > 0 ? (
                                            history.map((entry) => (
                                                <motion.div
                                                    key={entry.id}
                                                    className="p-4 bg-gray-700 bg-opacity-50 rounded-lg shadow-md"
                                                    initial={{opacity: 0, y: 10}}
                                                    animate={{opacity: 1, y: 0}}
                                                    transition={{duration: 0.3}}
                                                >
                                                    <div className="flex items-start gap-3">
                                                        <ChatBubbleLeftRightIcon
                                                            className="w-6 h-6 text-[var(--gold-light)] mt-1"/>
                                                        <div className="flex-1">
                                                            <p className="text-sm text-gray-300">
                                                                {moment(entry.created).format("D MMMM YYYY, HH:mm")} | {t("by")} {entry.created_by}
                                                            </p>
                                                            {entry.new_status ? (
                                                                <p className="text-lg font-semibold text-white">
                                                                    {t("status_changed")}:{" "}
                                                                    {entry.old_status ? (
                                                                        <>
                                                                            {statusOptions.find((opt) => opt.value === entry.old_status)?.label || entry.old_status} →{" "}
                                                                            {statusOptions.find((opt) => opt.value === entry.new_status)?.label || entry.new_status}
                                                                        </>
                                                                    ) : (
                                                                        statusOptions.find((opt) => opt.value === entry.new_status)?.label || entry.new_status
                                                                    )}
                                                                </p>
                                                            ) : (
                                                                <p className="text-lg font-semibold text-white">{t("note")}</p>
                                                            )}
                                                            <p className="text-gray-200">{entry.message}</p>
                                                            {entry.notify_client && (
                                                                <p className="text-sm text-[var(--gold-light)]">{t("client_notified")}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            ))
                                        ) : (
                                            <p className="text-gray-400">{t("no_history")}</p>
                                        )}
                                        {hasMore && (
                                            <div ref={ref} className="text-center">
                                                {isLoading && (
                                                    <p className="text-[var(--gold-light)]">{t("loading")}</p>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </motion.div>
                )}

                <motion.div
                    className="bg-gray-800 bg-opacity-90 backdrop-blur-md rounded-xl shadow-2xl border border-[var(--gold-light)]/20 p-8"
                    variants={cardVariants}
                    initial="hidden"
                    animate="visible"
                    whileHover="hover"
                >
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <h2 className="text-2xl font-heebo font-semibold text-[var(--gold-light)] mb-6 border-b border-[var(--gold-light)]/30 pb-2">
                                {t("plaintiff_details")}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                                <FormField
                                    control={form.control}
                                    name="tovea"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <UserIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_name")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="tovea_id"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <IdentificationIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_id")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff_id")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="tovea_birth"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <CakeIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_birth")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="date"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="tovea_living_city"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <MapPinIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_city")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff_city")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimant_attr_telephone"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <PhoneIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_phone")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff_phone")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimant_attr_cellphone"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <DevicePhoneMobileIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_mobile")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff_mobile")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimant_attr_fax"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <PrinterIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("plaintiff_fax")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_plaintiff_fax")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <h2 className="text-2xl font-heebo font-semibold text-[var(--gold-light)] mb-6 border-b border-[var(--gold-light)]/30 pb-2">
                                {t("defendant_details")}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                                <FormField
                                    control={form.control}
                                    name="nitba"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <UserIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_name")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="nitba_id"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <IdentificationIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_id")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_id")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="nitba_lawyer"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <BriefcaseIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_lawyer")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_lawyer")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimed_attorney_email"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <EnvelopeIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_email")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="email"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_email")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimed_attr_telephone"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <PhoneIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_phone")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_phone")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimed_attr_cellphone"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <DevicePhoneMobileIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_mobile")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_mobile")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="claimed_attr_fax"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <PrinterIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("defendant_fax")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_defendant_fax")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <h2 className="text-2xl font-heebo font-semibold text-[var(--gold-light)] mb-6 border-b border-[var(--gold-light)]/30 pb-2">
                                {t("case_details")}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                                <FormField
                                    control={form.control}
                                    name="file_number"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <HashtagIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("file_number")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_case_number")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="date_meeting"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <CalendarIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("meeting_date")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="date"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                {isAdminOrEditor && (
                                    <FormField
                                        control={form.control}
                                        name="status"
                                        render={({field}) => (
                                            <FormItem>
                                                <FormLabel
                                                    className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                    <TagIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                    {t("status")}
                                                </FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger
                                                            className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]">
                                                            <SelectValue placeholder={t("select_status")}/>
                                                        </SelectTrigger>
                                                        <SelectContent
                                                            className="bg-gray-700 text-white border-[var(--gold-light)]/30">
                                                            {statusOptions.map((option) => (
                                                                <SelectItem key={option.value} value={option.value}>
                                                                    {option.label}
                                                                </SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage/>
                                            </FormItem>
                                        )}
                                    />
                                )}
                                <FormField
                                    control={form.control}
                                    name="amount"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <CurrencyDollarIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("claim_amount")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_claim_amount")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="result_case"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <ClipboardDocumentCheckIcon
                                                    className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("case_result")}
                                            </FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    {...field}
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_case_result")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="final_amount"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <BanknotesIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("settlement_amount")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_settlement_amount")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="lwr_final_amount"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <WalletIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("lawyer_fees")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_lawyer_fees")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="expenses"
                                    render={({field}) => (
                                        <FormItem>
                                            <FormLabel
                                                className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                <ReceiptPercentIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                {t("expenses")}
                                            </FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                    placeholder={t("enter_expenses")}
                                                />
                                            </FormControl>
                                            <FormMessage/>
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <h2 className="text-2xl font-heebo font-semibold text-[var(--gold-light)] mb-6 border-b border-[var(--gold-light)]/30 pb-2">
                                {t("documents")}
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                                {lawsuit.ctav_tvia_url && (
                                    <div className="p-4 bg-gray-700 bg-opacity-50 rounded-lg">
                                        <label
                                            className="text-sm font-medium text-gray-200">{t("claim_document")}</label>
                                        <p className="text-lg font-semibold text-white flex items-center gap-2">
                                            <ArrowDownTrayIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                            <Link href={lawsuit.ctav_tvia_url} target="_blank"
                                                  className="text-[var(--gold-light)] hover:text-[var(--gold-light)]/80">
                                                {t("view_document")}
                                            </Link>
                                        </p>
                                    </div>
                                )}
                                {lawsuit.ctav_hagana_url && (
                                    <div className="p-4 bg-gray-700 bg-opacity-50 rounded-lg">
                                        <label
                                            className="text-sm font-medium text-gray-200">{t("defense_document")}</label>
                                        <p className="text-lg font-semibold text-white flex items-center gap-2">
                                            <ArrowDownTrayIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                            <Link href={lawsuit.ctav_hagana_url} target="_blank"
                                                  className="text-[var(--gold-light)] hover:text-[var(--gold-light)]/80">
                                                {t("view_document")}
                                            </Link>
                                        </p>
                                    </div>
                                )}
                                {lawsuit.settlement_agreement_url && (
                                    <div className="p-4 bg-gray-700 bg-opacity-50 rounded-lg">
                                        <label
                                            className="text-sm font-medium text-gray-200">{t("settlement_agreement")}</label>
                                        <p className="text-lg font-semibold text-white flex items-center gap-2">
                                            <ArrowDownTrayIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                            <Link href={lawsuit.settlement_agreement_url} target="_blank"
                                                  className="text-[var(--gold-light)] hover:text-[var(--gold-light)]/80">
                                                {t("view_document")}
                                            </Link>
                                        </p>
                                    </div>
                                )}
                            </div>

                            {(lawsuit.is_mutual_consent || lawsuit.cancellation_reason) && (
                                <>
                                    <h2 className="text-2xl font-heebo font-semibold text-[var(--gold-light)] mb-6 border-b border-[var(--gold-light)]/30 pb-2">
                                        {t("cancellation_details")}
                                    </h2>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
                                        {lawsuit.is_mutual_consent && (
                                            <FormField
                                                control={form.control}
                                                name="is_mutual_consent"
                                                render={({field}) => (
                                                    <FormItem className="flex items-center space-x-4">
                                                        <FormLabel
                                                            className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                            <UsersIcon className="w-5 h-5 text-[var(--gold-light)]"/>
                                                            {t("mutual_consent")}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Switch
                                                                checked={field.value === "1"}
                                                                onCheckedChange={(checked) => field.onChange(checked ? "1" : "0")}
                                                                className="data-[state=checked]:bg-[var(--gold-light)] data-[state=unchecked]:bg-[var(--red-elegant)]"
                                                            />
                                                        </FormControl>
                                                        <FormMessage/>
                                                    </FormItem>
                                                )}
                                            />
                                        )}
                                        {lawsuit.cancellation_reason && (
                                            <FormField
                                                control={form.control}
                                                name="cancellation_reason"
                                                render={({field}) => (
                                                    <FormItem className="col-span-2">
                                                        <FormLabel
                                                            className="text-sm font-medium text-gray-200 flex items-center gap-2">
                                                            <ChatBubbleLeftIcon
                                                                className="w-5 h-5 text-[var(--gold-light)]"/>
                                                            {t("cancellation_reason")}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Textarea
                                                                {...field}
                                                                className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                                placeholder={t("enter_cancellation_reason")}
                                                            />
                                                        </FormControl>
                                                        <FormMessage/>
                                                    </FormItem>
                                                )}
                                            />
                                        )}
                                    </div>
                                </>
                            )}

                            {isAdminOrEditor && (
                                <div className="flex justify-end mt-8 space-x-4">
                                    <Dialog open={openCancelDialog}
                                            onOpenChange={(open) => !isCanceled && setOpenCancelDialog(open)}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="destructive"
                                                className={`bg-[var(--red-elegant)] text-white ${isCanceled ? "opacity-50 bg-gray-600/50 cursor-not-allowed" : "hover:bg-[var(--red-elegant)]/80"}`}
                                                disabled={isCanceled}
                                                title={isCanceled ? t("already_canceled") : ""}
                                            >
                                                {t("cancel")}
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="bg-gray-800 text-white border-[var(--gold-light)]/20">
                                            <DialogHeader>
                                                <DialogTitle>{t("cancel_case")}</DialogTitle>
                                            </DialogHeader>
                                            <Form {...cancelForm}>
                                                <form onSubmit={cancelForm.handleSubmit(onCancelSubmit)}
                                                      className="space-y-4">
                                                    <FormField
                                                        control={cancelForm.control}
                                                        name="isMutualConsent"
                                                        render={({field}) => (
                                                            <FormItem className="flex items-center space-x-4">
                                                                <FormLabel
                                                                    className="text-sm font-medium">{t("mutual_consent")}</FormLabel>
                                                                <FormControl>
                                                                    <Switch
                                                                        checked={field.value}
                                                                        onCheckedChange={field.onChange}
                                                                        className="data-[state=checked]:bg-[var(--gold-light)] data-[state=unchecked]:bg-[var(--red-elegant)]"
                                                                    />
                                                                </FormControl>
                                                                <FormMessage/>
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        control={cancelForm.control}
                                                        name="cancellationReason"
                                                        render={({field}) => (
                                                            <FormItem>
                                                                <FormLabel
                                                                    className="text-sm font-medium">{t("cancellation_reason")}</FormLabel>
                                                                <FormControl>
                                                                    <Textarea
                                                                        {...field}
                                                                        className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                                        placeholder={t("enter_reason")}
                                                                    />
                                                                </FormControl>
                                                                <FormMessage/>
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <FormField
                                                        control={cancelForm.control}
                                                        name="settlementAgreement"
                                                        render={({field}) => (
                                                            <FormItem>
                                                                <FormLabel
                                                                    className="text-sm font-medium">{t("settlement_agreement")}</FormLabel>
                                                                <FormControl>
                                                                    <Input
                                                                        type="file"
                                                                        accept=".pdf,.doc,.docx"
                                                                        onChange={(e) => {
                                                                            const file = e.target.files?.[0];
                                                                            if (file && file.size > 10 * 1024 * 1024) {
                                                                                toast.error(t("file_size_exceeded"), {
                                                                                    description: t("file_size_exceeded_description"),
                                                                                    style: {
                                                                                        background: "var(--red-elegant)",
                                                                                        color: "#ffffff",
                                                                                        border: "1px solid var(--red-elegant)",
                                                                                    },
                                                                                });
                                                                                return;
                                                                            }
                                                                            field.onChange(file);
                                                                        }}
                                                                        className="bg-gray-700 text-white border-[var(--gold-light)]/30 focus:border-[var(--gold-light)]"
                                                                    />
                                                                </FormControl>
                                                                <FormMessage/>
                                                            </FormItem>
                                                        )}
                                                    />
                                                    <div className="flex justify-end space-x-4">
                                                        <Button
                                                            type="button"
                                                            onClick={() => setOpenCancelDialog(false)}
                                                            className="bg-[var(--red-elegant)] text-white hover:bg-[var(--red-elegant)]/80"
                                                        >
                                                            {t("close")}
                                                        </Button>
                                                        <Button
                                                            type="submit"
                                                            className="bg-[var(--gold-light)] text-gray-900 hover:bg-[var(--gold-light)]/80"
                                                        >
                                                            {t("submit")}
                                                        </Button>
                                                    </div>
                                                </form>
                                            </Form>
                                        </DialogContent>
                                    </Dialog>
                                    <Button
                                        type="submit"
                                        className="bg-[var(--gold-light)] text-gray-900 hover:bg-[var(--gold-light)]/80"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? t("submitting") : t("save")}
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        className="border-[var(--red-elegant)] text-[var(--red-elegant)] hover:bg-[var(--red-elegant)]/10"
                                        onClick={() => router.push("/dashboard/lawsuits")}
                                        disabled={isLoading}
                                    >
                                        {t("cancel")}
                                    </Button>
                                </div>
                            )}
                        </form>
                    </Form>
                </motion.div>
            </div>
        </div>
    );
}