import i18next from "i18next";
import {initReactI18next} from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

i18next
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        debug: process.env.NODE_ENV === "development",
        fallbackLng: "he",
        supportedLngs: ["en", "he", "ar", "zh"],
        defaultNS: "translation",
        ns: ["translation"],
        backend: {
            // loadPath: "/locales/{{lng}}/translation.json",
            loadPath: '/locales/{{lng}}/{{ns}}.json?v=' + new Date().getTime(),
        },
        interpolation: {
            escapeValue: false,
        },
        detection: {
            order: ["cookie", "navigator", "htmlTag", "path", "subdomain"],
            caches: ["cookie"],
            lookupCookie: "lang",
        },
        preload: ["he", "ar", "en", "zh"],
    });

export default i18next;